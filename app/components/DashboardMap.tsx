import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { useLocation } from '../contexts/LocationContext';
import useLocationServices from '../hooks/useLocationServices';
import axios from 'axios';
import { useRoute } from '../contexts/RouteContext';

const LOCATIONIQ_KEY = '***********************************';

// Debounce function to limit API calls
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Cache for storing route results
const routeCache = new Map();

// Memoized function to generate cache key
const getCacheKey = (start: { lat: number, lng: number }, end: { lat: number, lng: number }) => {
  return `${start.lat},${start.lng}-${end.lat},${end.lng}`;
};

export default function DashboardMap() {
  const webViewRef = useRef<WebView>(null);
  const { dropoffLocation } = useLocation();
  const { currentLocation, startForegroundTracking } = useLocationServices();
  const { routeGeometry, setRouteInfo, clearRouteInfo } = useRoute();
  const [isMapReady, setIsMapReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Use refs to track previous values and prevent unnecessary re-renders
  const prevDropoffRef = useRef<{ latitude: number; longitude: number; description: string } | null>(null);
  const prevRouteRef = useRef(null);

  const initialLocation = useMemo(() => {
    if (currentLocation?.coords) {
      return {
        lat: currentLocation.coords.latitude,
        lng: currentLocation.coords.longitude
      };
    }
    return { lat: 26.6293, lng: 87.9825 }; // Default to Birtamode
  }, [currentLocation]);

  // Initialize location tracking when component mounts
  useEffect(() => {
    startForegroundTracking().catch(error => {
      console.error('Error starting location tracking:', error);
    });

    // Clear route if no dropoff location on mount
    if (!dropoffLocation && routeGeometry) {
      clearRouteInfo();
    }
  }, []);

  // Memoize the fetchRoute function with stable dependencies
  const fetchRoute = useCallback(async () => {
    if (!currentLocation?.coords || !dropoffLocation || isLoading) {
      return;
    }

    const start = {
      lat: currentLocation.coords.latitude,
      lng: currentLocation.coords.longitude
    };
    const end = {
      lat: dropoffLocation.latitude,
      lng: dropoffLocation.longitude
    };
    const cacheKey = getCacheKey(start, end);

    // Check cache first
    if (routeCache.has(cacheKey)) {
      const cachedRoute = routeCache.get(cacheKey);
      setRouteInfo(cachedRoute.geometry, cachedRoute.distance);
      return;
    }

    setIsLoading(true);
    try {
      const response = await axios.get(
        'https://us1.locationiq.com/v1/directions/driving/' +
        `${start.lng},${start.lat};${end.lng},${end.lat}`,
        {
          params: {
            key: LOCATIONIQ_KEY,
            steps: true,
            alternatives: false,
            geometries: 'geojson',
            overview: 'full',
          },
        }
      );

      if (response.data?.routes?.[0]) {
        const route = response.data.routes[0];
        // Cache the result
        routeCache.set(cacheKey, {
          geometry: route.geometry,
          distance: Number(route.distance)
        });
        setRouteInfo(route.geometry, Number(route.distance));
      }
    } catch (error) {
      console.error('Error fetching route:', error);
      clearRouteInfo();
    } finally {
      setIsLoading(false);
    }
  }, [currentLocation?.coords, dropoffLocation, isLoading, setRouteInfo, clearRouteInfo]);

  // Debounced version of fetchRoute to prevent rapid calls
  const debouncedFetchRoute = useMemo(
    () => debounce(fetchRoute, 1000),
    [fetchRoute]
  );

  // Handle dropoff location changes
  useEffect(() => {
    // Skip if no dropoff location or no current location
    if (!dropoffLocation || !currentLocation?.coords) {
      return;
    }

    // Compare with previous dropoff location
    const prevDropoff = prevDropoffRef.current;
    const locationChanged =
      !prevDropoff ||
      prevDropoff.latitude !== dropoffLocation.latitude ||
      prevDropoff.longitude !== dropoffLocation.longitude;

    // Update ref with current value
    prevDropoffRef.current = dropoffLocation;

    // Only fetch if location actually changed
    if (locationChanged) {
      debouncedFetchRoute();
    }
  }, [dropoffLocation, currentLocation?.coords, debouncedFetchRoute]);

  // Update map markers and route
  const updateMapMarkers = useCallback(() => {
    if (!webViewRef.current || !isMapReady || !currentLocation) return;

    const markers = [];

    // Current location marker
    if (currentLocation?.coords) {
      markers.push({
        type: 'current',
        lat: currentLocation.coords.latitude,
        lng: currentLocation.coords.longitude,
      });
    }

    // Dropoff location marker
    if (dropoffLocation) {
      markers.push({
        type: 'dropoff',
        lat: dropoffLocation.latitude,
        lng: dropoffLocation.longitude,
      });
    }

    // Compare with previous route to prevent unnecessary updates
    const currentRouteStr = JSON.stringify(routeGeometry);
    const prevRouteStr = prevRouteRef.current;
    const routeChanged = currentRouteStr !== prevRouteStr;
    prevRouteRef.current = currentRouteStr;

    webViewRef.current.injectJavaScript(`
      try {
        // Clear and update markers
        updateMarkers(${JSON.stringify(markers)});
        
        // Route update logic with strict comparison
        const newRoute = ${routeGeometry ? JSON.stringify(routeGeometry) : 'null'};
        
        if (!newRoute) {
          // No route, clear existing
          window.routeLayer.clearLayers();
          window.currentRoute = null;
        } else {
          // Only update if route is genuinely different
          const currentRouteStr = window.currentRoute ? JSON.stringify(window.currentRoute) : '';
          const newRouteStr = JSON.stringify(newRoute);
          
          if (currentRouteStr !== newRouteStr) {
            window.routeLayer.clearLayers();
            window.currentRoute = newRoute;
            updateRoute(newRoute);
          }
        }
        
        ${dropoffLocation ? 'fitBounds();' : ''}
        true;
      } catch (error) {
        console.error('Map update error:', error);
        false;
      }
    `);
  }, [currentLocation, dropoffLocation, isMapReady, routeGeometry]);

  // Update map when relevant data changes
  useEffect(() => {
    if (isMapReady && currentLocation?.coords) {
      updateMapMarkers();
    }
  }, [updateMapMarkers, isMapReady, currentLocation]);

  const getMapHtml = useCallback(() => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
        <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
        <style>
          body { margin: 0; }
          #map { height: 100vh; }
          
          @keyframes precision-pulse {
            0% {
              transform: scale(1);
              box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
            }
            40% {
              transform: scale(1.1);
              box-shadow: 0 0 0 20px rgba(34, 197, 94, 0);
            }
            80% {
              transform: scale(1);
              box-shadow: 0 0 0 40px rgba(34, 197, 94, 0);
            }
            100% {
              transform: scale(1);
              box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
            }
          }

          @keyframes location-beacon {
            0% {
              transform: scale(0.8);
              opacity: 0.7;
            }
            50% {
              transform: scale(1.2);
              opacity: 0.5;
            }
            100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }

          .current-location-marker {
            position: relative;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .location-marker-core {
            width: 16px;
            height: 16px;
            background-color: #4CAF50;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
            position: relative;
            z-index: 10;
            animation: precision-pulse 2s infinite;
          }

          .location-marker-beacon {
            position: absolute;
            width: 30px;
            height: 30px;
            background-color: #4CAF50;
            border-radius: 50%;
            opacity: 0.3;
            animation: location-beacon 2s infinite;
            z-index: 5;
          }

          .you-are-here {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 10px;
            border-radius: 16px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 15;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
          }

          .dropoff-marker {
            position: relative;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .dropoff-marker .location-marker-core {
            width: 16px;
            height: 16px;
            background-color: #D32F2F;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
            position: relative;
            z-index: 10;
            animation: precision-pulse 2s infinite;
          }

          .dropoff-marker .location-marker-beacon {
            position: absolute;
            width: 30px;
            height: 30px;
            background-color: #D32F2F;
            border-radius: 50%;
            opacity: 0.3;
            animation: location-beacon 2s infinite;
            z-index: 5;
          }

          .destination-callout {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 10px;
            border-radius: 16px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 15;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
          }
        </style>
      </head>
      <body>
        <div id="map"></div>
        <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
        <script>
          var map;
          var markers = [];
          var markerGroup;
          var routeLayer;
          var currentRoute = null;
          
          try {
            // Initialize map
            map = L.map('map', {
              zoomControl: false,
              attributionControl: false
            });
            
            // Add tile layer
        
                 L.tileLayer('https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}&scale=3', {
                      maxZoom: 25,
                      minZoom: 1,
                      subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
                      tileSize: 256,
                      updateWhenIdle: true,
                      keepBuffer: 2
                  }).addTo(map);

            // Initialize marker group and route layer
            markerGroup = L.featureGroup().addTo(map);
            routeLayer = L.geoJSON().addTo(map);

            // Set initial view (Kathmandu)
              ${initialLocation
        ? `map.setView([${initialLocation.lat}, ${initialLocation.lng}], 16);`
        : 'map.setView([27.7172, 85.3240], 16);' // Fallback to Kathmandu only if no location available
      };

            // Notify React Native that the map is ready
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'mapReady'
            }));

            function updateMarkers(newMarkers) {
              markerGroup.clearLayers();
              newMarkers.forEach(marker => {
                let icon;
                
                if (marker.type === 'current') {
                  icon = L.divIcon({
                    className: 'current-location-marker',
                    html: '<div class="location-marker-beacon"></div><div class="location-marker-core"></div><div class="you-are-here">Your Location</div>',
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                  });
                } else {
                  icon = L.divIcon({
                    className: 'dropoff-marker',
                    html: '<div class="location-marker-beacon"></div><div class="location-marker-core"></div><div class="destination-callout">Your Destination</div>',
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                  });
                }
                
                L.marker([marker.lat, marker.lng], { icon }).addTo(markerGroup);
              });
            }

            function updateRoute(geometry) {
              if (!geometry) return;
              
              const route = {
                type: "Feature",
                properties: {},
                geometry: geometry
              };
              
              routeLayer.addData(route);
              routeLayer.setStyle({
                color: '#4A90E2',
                weight: 6,
                opacity: 0.8
              });
            }

            function fitBounds() {
              const bounds = markerGroup.getBounds();
              map.fitBounds(bounds, {
              padding: [50, 50],
                maxZoom: 16
              });
            }

          } catch (error) {
            console.error('Error initializing map:', error);
          }
        </script>
      </body>
      </html>
    `;
  }, [initialLocation]);

  if (!currentLocation) return (
    <View>
      <Text>Loading location...</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <WebView
        ref={webViewRef}
        source={{ html: getMapHtml() }}
        style={styles.map}
        onMessage={(event) => {
          const data = JSON.parse(event.nativeEvent.data);
          if (data.type === 'mapReady') {
            setIsMapReady(true);
          }
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
});
