{"common": {"cancel": "Cancel", "confirm": "Confirm", "ok": "OK", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error", "success": "Success", "account": "Account", "support": "Support", "back": "Back", "pickup": "Pickup", "dropoff": "Dropoff", "callDriver": "Call Driver", "status": {"completed": "Completed", "cancelled": "Cancelled", "pending": "Pending", "accepted": "Accepted"}, "noData": "No data found", "endOfList": "End of list", "rides": {"available": "{{count}} {{plural}} available", "noRides": "Sorry, currently no rides available", "ride": "ride", "rides": "rides"}, "offline": {"title": "No internet connection", "message": "It looks like you're offline. Please check your internet connection and try again when you're back online."}, "continue": "Continue Using App", "cancelReasons": {"driverNotMoving": "Driver not moving", "driverTooFar": "Driver is too far", "wrongLocation": "Wrong pickup location", "driverNotResponding": "Driver not responding", "longWait": "Long waiting time", "changedMind": "Changed my mind", "foundAnotherRide": "Found another ride", "priceTooHigh": "Price too high", "safetyConcerns": "Safety concerns", "bookedByMistake": "Booked by mistake", "other": "Other", "pleaseSpecify": "Please specify your reason"}, "other": "Other"}, "mapView": {"loading": "Loading map...", "findNearbyCabs": "View Nearby Rides", "selectCancellationReason": "Select Cancellation Reason", "cancelReasonDescription": "Please select a reason for cancellation", "specifyReason": "Please specify", "cancelReason": "Cancellation Reason", "confirmCancellation": "Confirm Cancellation", "cancellingRide": "Cancelling Ride", "cancelConfirmMessage": "Are you sure you want to cancel this ride?", "confirm": "Confirm", "cancel": "Cancel", "centerLocation": "Center Location", "rateCalulation": "Calculate fare"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Log out", "signUp": "Register", "forgotPassword": "Forgot Password?", "confirmLogout": "Are you sure you want to log out of your account?", "troubleLogin": "Having trouble logging in?", "useEmail": "Use email instead", "backToOptions": "Back to Other Options", "continueWithGoogle": "Continue with Google", "continueWithApple": "Continue with Apple", "continueWithEmail": "Continue with <PERSON>ail"}, "update": {"required": {"title": "Update Required", "message": "A new version of the app is required to continue using the app. Please update to the latest version.", "blocking": "Please update the app to continue. The app cannot function with the current version."}, "available": {"title": "Update Available", "message": "A new version of the app is available. Would you like to update now?"}, "button": {"update": "Update Now", "later": "Later"}, "error": {"store": "Could not open the app store. Please update manually.", "check": "Failed to check for updates. Please try again later."}}, "settings": {"title": "Settings", "appearance": "Appearance", "darkMode": "Dark Mode", "profile": "Profile", "contactSupport": "Contact Support", "supportDescription": "Need help? We're here for you", "language": "Language", "notifications": "Notifications", "pushNotifications": "Push Notifications", "pushNotificationsDesc": "Get notified about new ride requests and updates", "rideHistory": {"title": "Ride History", "noRides": "No ride history found", "loadingError": "Error loading ride history", "cancelReason": "Cancelled: {{reason}}"}, "rateApp": "Rate App", "rateDescription": "Love the app? Let us know!", "bloodGroup": "Blood Group"}, "languages": {"english": "English", "nepali": "नेपाली"}, "rideHistory": {"title": "Ride History", "noRides": "No ride history found", "pickup": "Pickup", "dropoff": "Dropoff", "cancelled": "Cancelled", "loadMore": "Load More"}, "ride": {"requestRide": "Request a Ride", "viewAvailableRides": "View All Available Rides", "shareLocation": "Share Location", "call": "Call", "currentLocation": "Your Current Location", "selectedLocation": "Selected Pickup Location", "availableCab": "Available Cab", "confirmRide": "Confirm Ride", "rideConfirmationMessage": "Are you sure you want to request a ride to this location?", "cancelRide": "Cancel Ride", "ridesNearby": "Rides Nearby", "selectCancellationReason": "Select a Cancellation Reason", "cancelReason": "Cancellation Reason", "status": {"pendingMessage": "Ride requested! You can call the driver", "acceptedMessage": "Your ride has been accepted"}}, "nearbyCabs": {"title": "Nearby Rides", "away": "away", "book": "Bolaoo", "cancel": "Cancel", "noCabs": "No rides available nearby", "selectCancelReason": "Select Cancellation Reason", "cancelReason": "Cancellation Reason", "noDrivers": "No drivers available nearby", "loading": "Loading...", "confirmBooking": "Confirm Booking", "confirmBookingMessage": "Your ride request will be sent to the driver and you can call them immediately.", "distanceError": "Can't book, driver is far away from your location"}, "profile": {"title": "Profile", "loadingInfo": "Loading profile information...", "errorLoading": "Error loading profile", "personalInfo": "Personal Information", "accountActions": "Account Actions", "profileName": "Name", "phoneNumber": "Phone Number", "email": "Email", "editProfile": "Edit Profile", "signOut": "Sign Out", "deleteAccount": "Delete Account", "deleteAccountConfirmTitle": "Delete Account", "deleteAccountConfirmMessage": "Please review the following information about account deletion:", "deleteAccountReviewInfo": "Your request will be reviewed within 30 days to ensure:\n• No pending rides or payments\n• No active bookings\n• No ongoing disputes\n• Proper verification of account ownership", "deleteAccountDataInfo": "Once approved, we will permanently delete:\n• Personal Information (name, email, phone)\n• Profile Information\n• Ride History\n• Payment Information\n• Profile Pictures", "deleteAccountReason": "Reason for deletion (optional)", "deleteAccountReasonPlaceholder": "Please tell us why you want to delete your account", "requestDelete": "Request Deletion", "deleteAccountRequestedTitle": "Request Submitted", "deleteAccountRequestedMessage": "Your account deletion request has been submitted. We'll review your request within 30 days and send a confirmation email once the deletion is complete. You can continue using your account until the deletion is processed.", "deleteAccountError": "Failed to submit deletion request. Please try again later.", "bloodDonation": {"title": "Blood Donation Consent", "description": "Your blood group information may help in emergency situations. Do you authorize Bolaoo Rides to contact you for emergency blood donation requests in your area?", "yes": "Yes", "no": "No"}}, "dashboard": {"welcome": "Welcome", "accessDenied": "Access Denied", "driverMessage": "This app is for passengers only. Please use the driver app instead.", "okay": "OK", "loading": "Loading...", "errorLoading": "Error loading profile data", "returnToLogin": "Return to Login", "whereToGo": "Where would you like to go?", "greetings": "Greetings, {{name}}", "readyToRide": "Ready for your next journey? 🚗"}, "errors": {"signOutError": "There was a problem signing out. Please try again.", "loadingError": "Error loading data", "locationPermission": "Location permission denied. Please enable location services.", "rideRequestFailed": "Failed to request ride. Please try again."}}