
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { StyleSheet, View, Animated, TouchableOpacity } from 'react-native';
import { Text, IconButton, TextInput, Surface } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';
import * as Location from 'expo-location';
import axios from 'axios';
import { router } from 'expo-router';
import { useLocation } from '../contexts/LocationContext';
import useLocationServices from 'app/hooks/useLocationServices';

const LOCATION_IQ_API_KEY = '***********************************';
const BASE_URL = 'https://us1.locationiq.com/v1';

// Debounce function to limit API calls
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

interface LocationType {
  latitude: number;
  longitude: number;
  description: string;
}

export default function DropoffLocation() {
  const mapRef = useRef<WebView>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [currentMapAddress, setCurrentMapAddress] = useState<LocationType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { setDropoffLocation, dropoffLocation } = useLocation();
  const { currentLocation, startForegroundTracking } = useLocationServices();

  // Animation values for the jumping pin
  const pinAnimation = useRef(new Animated.Value(0)).current;
  const shadowAnimation = useRef(new Animated.Value(1)).current;

  // Start the jumping animation when component mounts
  useEffect(() => {
    const startJumpingAnimation = () => {
      Animated.loop(
        Animated.sequence([
          Animated.parallel([
            Animated.timing(pinAnimation, {
              toValue: -8, // Move up by 8 pixels
              duration: 600,
              useNativeDriver: true,
            }),
            Animated.timing(shadowAnimation, {
              toValue: 0.6, // Reduce shadow opacity when pin is up
              duration: 600,
              useNativeDriver: true,
            }),
          ]),
          Animated.parallel([
            Animated.timing(pinAnimation, {
              toValue: 0, // Move back to original position
              duration: 600,
              useNativeDriver: true,
            }),
            Animated.timing(shadowAnimation, {
              toValue: 1, // Restore shadow opacity when pin is down
              duration: 600,
              useNativeDriver: true,
            }),
          ]),
          Animated.delay(1500), // Pause for 1.5 seconds before next jump
        ])
      ).start();
    };

    startJumpingAnimation();
  }, [pinAnimation, shadowAnimation]);

  // Initialize location tracking when component mounts
  useEffect(() => {
    const initializeLocation = async () => {
      try {
        await startForegroundTracking();
      } catch (error) {
        console.error('Error starting location tracking:', error);
      }
    };

    initializeLocation();
  }, [startForegroundTracking]);

  const initialLocation = useMemo(() => {
    if (dropoffLocation) {
      return {
        lat: dropoffLocation.latitude,
        lng: dropoffLocation.longitude
      };
    }
    if (currentLocation?.coords) {
      return {
        lat: currentLocation.coords.latitude,
        lng: currentLocation.coords.longitude
      };
    }
    return { lat: 27.7172, lng: 85.3240 }; // Default to Kathmandu
  }, [dropoffLocation, currentLocation]);

  const fetchSearchResults = useCallback(
    debounce(async (query: string) => {
      if (!query) return;
      setIsLoading(true);
      try {
        const response = await axios.get(`${BASE_URL}/autocomplete.php`, {
          params: {
            key: LOCATION_IQ_API_KEY,
            q: query,
            format: 'json'
          }
        });
        setSearchResults(response.data);
      } catch (error) {
        console.error('Error fetching search results:', error);
      } finally {
        setIsLoading(false);
      }
    }, 1000),
    []
  );

  useEffect(() => {
    fetchSearchResults(searchQuery);
  }, [searchQuery, fetchSearchResults]);

  useEffect(() => {
    if (!mapRef.current) return;

    mapRef.current.injectJavaScript(`
      try {
        map.setView([${initialLocation.lat}, ${initialLocation.lng}], 16);
        true;
      } catch (error) {
        console.error('Error setting map view:', error);
        false;
      }
    `);
  }, [initialLocation]);

  const debouncedGetAddress = useMemo(
    () =>
      debounce(async (lat: number, lng: number) => {
        if (!lat || !lng) return;
        setIsLoading(true);
        try {
          const response = await axios.get(
            `${BASE_URL}/reverse?key=${LOCATION_IQ_API_KEY}&lat=${lat}&lon=${lng}&format=json`,
            {
              timeout: 10000, // 10 second timeout
            }
          );
          const address = response.data.display_name;
          setCurrentMapAddress({
            latitude: lat,
            longitude: lng,
            description: address,
          });
        } catch (error: any) {
          console.error('Error fetching address:', error);

          // Handle rate limiting specifically
          if (error.response?.status === 429) {
            console.warn('Rate limit exceeded for address lookup');
            // Don't clear the address, just log the warning
            return;
          }

          setCurrentMapAddress(null);
        } finally {
          setIsLoading(false);
        }
      }, 2000), // Increased debounce to 2 seconds to reduce API calls
    []
  );

  const handleMapMoveEnd = useCallback(
    (event: { lat: number; lng: number }) => {
      const { lat, lng } = event;
      if (lat && lng) {
        debouncedGetAddress(lat, lng);
      }
    },
    [debouncedGetAddress]
  );

  const handleSetCurrentLocation = useCallback(async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.error('Permission to access location was denied');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;
      debouncedGetAddress(latitude, longitude);
      fitToLocation({ latitude, longitude });
    } catch (error) {
      console.error('Error getting current location:', error);
    }
  }, [debouncedGetAddress]);

  const fitToLocation = useCallback((location: { latitude: number; longitude: number }) => {
    if (!mapRef.current) return;

    const js = `
      (function() {
        try {
          if (typeof map === 'undefined') return;
          map.setView([${location.latitude}, ${location.longitude}], 20);
          true;
        } catch (error) {
          console.error('Error in fitToLocation:', error);
          false;
        }
      })();
    `;

    requestAnimationFrame(() => {
      mapRef.current?.injectJavaScript(js);
    });
  }, []);

  const searchLocation = useCallback(
    async (text: string) => {
      if (text.length > 2) {
        try {
          const response = await axios.get(
            `${BASE_URL}/autocomplete?key=${LOCATION_IQ_API_KEY}&q=${encodeURIComponent(text)}&format=json&limit=5&countrycodes=np`
          );
          setSearchResults(response.data || []);
        } catch (error) {
          console.error('Error searching location:', error);
          setSearchResults([]);
        }
      } else {
        setSearchResults([]);
      }
    },
    []
  );

  const debouncedSearchLocation = useMemo(() => debounce(searchLocation, 600), [searchLocation]);

  const handleLocationSelect = useCallback((location: any) => {
    const { lat, lon, display_name } = location;
    const newLocation = {
      latitude: parseFloat(lat),
      longitude: parseFloat(lon),
    };
    setCurrentMapAddress({
      latitude: newLocation.latitude,
      longitude: newLocation.longitude,
      description: display_name,
    });
    fitToLocation(newLocation);
    setSearchResults([]);
    setSearchQuery('');
  }, [fitToLocation]);

  const handleConfirmLocation = useCallback(() => {
    if (currentMapAddress) {
      setDropoffLocation({
        latitude: currentMapAddress.latitude,
        longitude: currentMapAddress.longitude,
        description: currentMapAddress.description,
      });
      // router.back();
      router.push('/dashboard-map')

    }
  }, [currentMapAddress, setDropoffLocation]);

  const getMapHtml = useCallback(() => {
    const currentCoords = currentLocation?.coords;
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
        <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
        <style>
          body { margin: 0; }
          #map { height: 100vh; }
          
          @keyframes precision-pulse {
            0% {
              transform: scale(1);
              box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
            }
            40% {
              transform: scale(1.1);
              box-shadow: 0 0 0 20px rgba(34, 197, 94, 0);
            }
            80% {
              transform: scale(1);
              box-shadow: 0 0 0 40px rgba(34, 197, 94, 0);
            }
            100% {
              transform: scale(1);
              box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
            }
          }

          @keyframes location-beacon {
            0% {
              transform: scale(0.8);
              opacity: 0.7;
            }
            50% {
              transform: scale(1.2);
              opacity: 0.5;
            }
            100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }

          .custom-marker-icon {
            position: relative;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .location-marker-core {
            width: 16px;
            height: 16px;
            background-color: #22c55e;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
            position: relative;
            z-index: 10;
            animation: precision-pulse 2s infinite;
          }

          .location-marker-beacon {
            position: absolute;
            width: 30px;
            height: 30px;
            background-color: #22c55e;
            border-radius: 50%;
            opacity: 0.3;
            animation: location-beacon 2s infinite;
            z-index: 5;
          }

          .you-are-here {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 10px;
            border-radius: 16px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 15;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
          }
        </style>
      </head>
      <body>
        <div id="map"></div>
        <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
        <script>
          var map;
          var currentLocationMarker;
          
          try {
            map = L.map('map', {
              maxZoom: 20,
              minZoom: 5
            });
            
            L.tileLayer('https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}&scale=3', {
              maxZoom: 25,
              minZoom: 1,
              subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
              tileSize: 256,
              updateWhenIdle: true,
              keepBuffer: 2
            }).addTo(map);

            // Set initial view based on current or dropoff location
            ${initialLocation
        ? `map.setView([${initialLocation.lat}, ${initialLocation.lng}], 16);`
        : 'map.setView([27.7172, 85.3240], 13);' // Fallback to Kathmandu only if no location available
      }

            // Add current location marker if available
            ${currentCoords ? `
              // Create custom marker icon for current location
              var customIcon = L.divIcon({
                className: 'custom-marker-icon',
                html: \`
                  <div class="location-marker-beacon"></div>
                  <div class="location-marker-core"></div>
                  <div class="you-are-here">You are here</div>
                \`,
                iconSize: [30, 30],
                iconAnchor: [15, 15]
              });

              // Add marker for current location
              currentLocationMarker = L.marker([${currentCoords.latitude}, ${currentCoords.longitude}], {
                icon: customIcon
              })
              .addTo(map);
            ` : ''}
            
            // Add event listeners for map movement
            map.on('moveend dragend', function() {
              const center = map.getCenter();
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'centerLocation',
                lat: center.lat,
                lng: center.lng
              }));
            });

          } catch (error) {
            console.error('Error initializing map:', error);
          }
        </script>
      </body>
      </html>
    `;
  }, [initialLocation, currentLocation]);

  return (
    <View style={styles.container}>
      <WebView
        ref={mapRef}
        source={{
          html: getMapHtml(),
        }}
        style={styles.map}
        onMessage={(event) => {
          const data = JSON.parse(event.nativeEvent.data);
          if (data.type === 'centerLocation') {
            handleMapMoveEnd(data);
          }
        }}
      />

      <Animated.View
        style={[
          styles.pinContainer,
          {
            transform: [
              { translateY: pinAnimation }
            ],
            shadowOpacity: shadowAnimation,
          }
        ]}
      >
        <MaterialCommunityIcons name="map-marker" size={48} color={"#D32F2F"} />
      </Animated.View>

      <View style={styles.searchContainer}>
        <Surface style={styles.searchBar} elevation={4}>
          <IconButton icon="arrow-left" size={24} onPress={() => router.back()} />
          <TextInput
            placeholder="Search location"
            value={searchQuery}
            onChangeText={(text) => {
              setSearchQuery(text);
              debouncedSearchLocation(text);
            }}
            style={styles.searchInput}
            right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : undefined}
          />
        </Surface>

        {searchResults.length > 0 && (
          <Surface style={styles.searchResults} elevation={4}>
            {searchResults.map((result, index) => (
              <Surface
                key={`${result.place_id}-${index}`}
                style={styles.resultItem}
                onTouchEnd={() => handleLocationSelect(result)}
              >
                <MaterialCommunityIcons name="map-marker-outline" size={24} color="#666" />
                <Text style={styles.resultText}>{result.display_name}</Text>
              </Surface>
            ))}
          </Surface>
        )}
      </View>

      <View style={styles.bottomContainer}>
        <View style={styles.addressContainer}>
          <MaterialCommunityIcons
            name="map-marker-radius-outline"
            size={20}
            color="#666"
            style={styles.addressIcon}
          />
          <View style={styles.addressTextContainer}>
            <Text style={styles.addressLabel}>Destination</Text>
            {isLoading ? (
              <Text style={styles.loadingText}>Getting location...</Text>
            ) : (
              <Text numberOfLines={2} style={styles.addressText}>
                {currentMapAddress?.description || 'Move map to select destination'}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <IconButton
            icon="crosshairs-gps"
            mode="contained"
            size={24}
            onPress={handleSetCurrentLocation}
            style={styles.gpsButton}
            containerColor="#E0F2F1"
            iconColor="#009688"
          />
          <TouchableOpacity
            style={[
              styles.confirmButton,
              {
                backgroundColor: !currentMapAddress || isLoading ? '#ccc' : '#149863',
                opacity: !currentMapAddress || isLoading ? 0.7 : 1
              }
            ]}
            onPress={handleConfirmLocation}
            disabled={!currentMapAddress || isLoading}
          >
            <MaterialCommunityIcons
              name="map-marker-check"
              size={24}
              color="#fff"
            />
            <Text style={styles.confirmButtonText}>
              Confirm Destination
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  pinContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -24 }, { translateY: -48 }],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchContainer: {
    position: 'absolute',
    top: 16,
    left: 16,
    right: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    backgroundColor: 'rgba(255,255,255,0.95)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  searchResults: {
    borderRadius: 12,
    backgroundColor: 'rgba(255,255,255,0.95)',
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  resultText: {
    marginLeft: 12,
    flex: 1,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 14,
    paddingVertical: 10,
    marginBottom: 12,
    backgroundColor: 'rgba(255,255,255,0.98)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  addressIcon: {
    marginTop: 2,
    marginRight: 10,
  },
  addressTextContainer: {
    flex: 1,
  },
  addressLabel: {
    fontSize: 11,
    color: '#666',
    marginBottom: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    fontWeight: '600',
  },
  addressText: {
    fontSize: 14,
    color: '#1a1a1a',
    lineHeight: 19,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    lineHeight: 19,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  gpsButton: {
    backgroundColor: '#E0F2F1', // Light teal background
    borderRadius: 12,
    marginRight: 8,
  },
  confirmButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 12,
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
});
