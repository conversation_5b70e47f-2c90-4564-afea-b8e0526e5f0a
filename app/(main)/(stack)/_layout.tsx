import { Stack } from 'expo-router';
import React from 'react';

export default function StackLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="onboarding"
        options={{
          title: 'Complete Your Profile',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="onboarding-user"
        options={{
          title: 'Complete Your Profile',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="nearby-cabs"
        options={{
          title: 'Nearby Cabs',
          headerShown: false,
          animation: 'slide_from_bottom',
        }}
      />
      <Stack.Screen
        name="request-permissions"
        options={{
          title: 'Request Permissions',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="dropoff-location"
        options={{
          title: 'Drop Off Location',
          headerShown: false,
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="ride-tracking"
        options={{
          title: 'Ride Tracking',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="settings"
        options={{
          title: 'Settings',
          headerShown: false,
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="settings/notifications"
        options={{
          title: 'Notifications',
          headerShown: false,
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="pending-approval"
        options={{
          title: 'Pending Approval',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="settings/profile"
        options={{
          headerShown: false,
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="settings/ride-history"
        options={{
          headerShown: false,
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="settings/help"
        options={{
          headerShown: false,
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="settings/contact"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="settings/terms"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="settings/privacy"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="settings/vehicle"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="settings/payment"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="settings/documents"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="settings/support"
        options={{
          headerShown: false,
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="settings/feedback"
        options={{
          title: 'Feedback',
          headerShown: false,
        }}
      />
    </Stack>
  );
}
