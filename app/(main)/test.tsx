import React, { useState } from 'react';
import { 
  StyleSheet, 
  View, 
  ScrollView, 
  Animated, 
  TouchableOpacity,
  Alert 
} from 'react-native';
import { 
  Text, 
  Surface, 
  Button, 
  Card, 
  IconButton,
  Switch,
  Chip,
  ProgressBar
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../../src/theme/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function TestScreen() {
  const { theme } = useTheme();
  const [switchValue, setSwitchValue] = useState(false);
  const [progress, setProgress] = useState(0.3);
  const [selectedChip, setSelectedChip] = useState('option1');

  const animatedValue = new Animated.Value(0);

  const startAnimation = () => {
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const showAlert = () => {
    Alert.alert(
      'Test Alert',
      'This is a test alert to demonstrate functionality.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'OK', onPress: () => console.log('OK Pressed') },
      ]
    );
  };

  const animatedStyle = {
    transform: [
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [1, 1.1],
        }),
      },
    ],
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <View style={styles.header}>
          <MaterialCommunityIcons 
            name="flask" 
            size={32} 
            color={theme.colors.primary} 
          />
          <Text variant="headlineMedium" style={[styles.title, { color: theme.colors.onBackground }]}>
            Test Components
          </Text>
          <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
            Interactive UI component testing
          </Text>
        </View>

        {/* Animated Card */}
        <Animated.View style={animatedStyle}>
          <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <View style={styles.cardHeader}>
                <MaterialCommunityIcons 
                  name="animation-play" 
                  size={24} 
                  color={theme.colors.primary} 
                />
                <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                  Animation Test
                </Text>
              </View>
              <Text variant="bodyMedium" style={[styles.cardDescription, { color: theme.colors.onSurfaceVariant }]}>
                Tap the button to see a scale animation effect
              </Text>
              <Button 
                mode="contained" 
                onPress={startAnimation}
                style={styles.button}
                icon="play"
              >
                Start Animation
              </Button>
            </Card.Content>
          </Card>
        </Animated.View>

        {/* Controls Card */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons 
                name="tune" 
                size={24} 
                color={theme.colors.secondary} 
              />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                Interactive Controls
              </Text>
            </View>

            {/* Switch */}
            <View style={styles.controlRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                Enable notifications
              </Text>
              <Switch 
                value={switchValue} 
                onValueChange={setSwitchValue}
                thumbColor={switchValue ? theme.colors.primary : theme.colors.outline}
              />
            </View>

            {/* Progress Bar */}
            <View style={styles.controlSection}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
                Progress: {Math.round(progress * 100)}%
              </Text>
              <ProgressBar 
                progress={progress} 
                color={theme.colors.primary}
                style={styles.progressBar}
              />
              <View style={styles.progressButtons}>
                <Button 
                  mode="outlined" 
                  onPress={() => setProgress(Math.max(0, progress - 0.1))}
                  compact
                >
                  -10%
                </Button>
                <Button 
                  mode="outlined" 
                  onPress={() => setProgress(Math.min(1, progress + 0.1))}
                  compact
                >
                  +10%
                </Button>
              </View>
            </View>

            {/* Chips */}
            <View style={styles.controlSection}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
                Select an option:
              </Text>
              <View style={styles.chipContainer}>
                {['option1', 'option2', 'option3'].map((option) => (
                  <Chip
                    key={option}
                    selected={selectedChip === option}
                    onPress={() => setSelectedChip(option)}
                    style={styles.chip}
                    textStyle={{ fontFamily: 'Poppins-Medium' }}
                  >
                    {option.charAt(0).toUpperCase() + option.slice(1)}
                  </Chip>
                ))}
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons Card */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons 
                name="gesture-tap" 
                size={24} 
                color={theme.colors.tertiary} 
              />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                Action Buttons
              </Text>
            </View>

            <View style={styles.buttonGrid}>
              <Button 
                mode="contained" 
                onPress={showAlert}
                style={[styles.gridButton, { backgroundColor: theme.colors.primary }]}
                icon="alert"
              >
                Show Alert
              </Button>
              
              <Button 
                mode="outlined" 
                onPress={() => console.log('Outlined button pressed')}
                style={styles.gridButton}
                icon="information"
              >
                Info
              </Button>
              
              <Button 
                mode="text" 
                onPress={() => console.log('Text button pressed')}
                style={styles.gridButton}
                icon="help"
              >
                Help
              </Button>
              
              <TouchableOpacity 
                style={[styles.customButton, { backgroundColor: theme.colors.secondary }]}
                onPress={() => console.log('Custom button pressed')}
              >
                <MaterialCommunityIcons name="star" size={20} color="white" />
                <Text style={styles.customButtonText}>Custom</Text>
              </TouchableOpacity>
            </View>
          </Card.Content>
        </Card>

        {/* Status Card */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons 
                name="information" 
                size={24} 
                color={theme.colors.primary} 
              />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                System Status
              </Text>
            </View>
            
            <View style={styles.statusGrid}>
              <Surface style={[styles.statusItem, { backgroundColor: theme.colors.primaryContainer }]}>
                <MaterialCommunityIcons name="check-circle" size={24} color={theme.colors.primary} />
                <Text variant="bodySmall" style={{ color: theme.colors.onPrimaryContainer, textAlign: 'center' }}>
                  All Systems Operational
                </Text>
              </Surface>
              
              <Surface style={[styles.statusItem, { backgroundColor: theme.colors.secondaryContainer }]}>
                <MaterialCommunityIcons name="wifi" size={24} color={theme.colors.secondary} />
                <Text variant="bodySmall" style={{ color: theme.colors.onSecondaryContainer, textAlign: 'center' }}>
                  Network Connected
                </Text>
              </Surface>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 16,
  },
  title: {
    fontFamily: 'Poppins-Bold',
    marginTop: 8,
  },
  subtitle: {
    fontFamily: 'Poppins-Regular',
    marginTop: 4,
    textAlign: 'center',
  },
  card: {
    marginBottom: 16,
    borderRadius: 16,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardDescription: {
    fontFamily: 'Poppins-Regular',
    marginBottom: 16,
    marginLeft: 32,
  },
  button: {
    marginLeft: 32,
    borderRadius: 12,
  },
  controlRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: 32,
    marginBottom: 16,
  },
  controlSection: {
    marginLeft: 32,
    marginBottom: 16,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  progressButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  chip: {
    borderRadius: 16,
  },
  buttonGrid: {
    marginLeft: 32,
    gap: 12,
  },
  gridButton: {
    borderRadius: 12,
  },
  customButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 12,
    gap: 8,
  },
  customButtonText: {
    color: 'white',
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  statusGrid: {
    flexDirection: 'row',
    gap: 12,
    marginLeft: 32,
  },
  statusItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    gap: 8,
  },
});
